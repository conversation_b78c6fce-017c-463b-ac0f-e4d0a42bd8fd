-- 注意：该页面对应的前台目录为views/emsrepairorderapprovaltemplates文件夹下
-- 如果你想更改到其他目录，请修改sql中component字段对应的值


INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external)
VALUES ('2025080109504390130', NULL, '审批模板表', '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplatesList', 'emsrepairorderapprovaltemplates/EmsRepairOrderApprovalTemplatesList', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 1, 0, 0, 0, 0, NULL, '1', 0, 0, 'admin', '2025-08-01 09:50:13', NULL, NULL, 0);

-- 权限控制sql
-- 新增
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025080109504390131', '2025080109504390130', '添加审批模板表', NULL, NULL, 0, NULL, NULL, 2, 'emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:add', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-08-01 09:50:13', NULL, NULL, 0, 0, '1', 0);
-- 编辑
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025080109504390132', '2025080109504390130', '编辑审批模板表', NULL, NULL, 0, NULL, NULL, 2, 'emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:edit', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-08-01 09:50:13', NULL, NULL, 0, 0, '1', 0);
-- 删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025080109504390133', '2025080109504390130', '删除审批模板表', NULL, NULL, 0, NULL, NULL, 2, 'emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:delete', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-08-01 09:50:13', NULL, NULL, 0, 0, '1', 0);
-- 批量删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025080109504390134', '2025080109504390130', '批量删除审批模板表', NULL, NULL, 0, NULL, NULL, 2, 'emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:deleteBatch', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-08-01 09:50:13', NULL, NULL, 0, 0, '1', 0);
-- 导出excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025080109504390135', '2025080109504390130', '导出excel_审批模板表', NULL, NULL, 0, NULL, NULL, 2, 'emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:exportXls', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-08-01 09:50:13', NULL, NULL, 0, 0, '1', 0);
-- 导入excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2025080109504390136', '2025080109504390130', '导入excel_审批模板表', NULL, NULL, 0, NULL, NULL, 2, 'emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:importExcel', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2025-08-01 09:50:13', NULL, NULL, 0, 0, '1', 0);
