import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '模板名称',
    align:"center",
    dataIndex: 'templateName',
    width: 200,
    fixed: 'left'
   },
   {
    title: '状态',
    align:"center",
    dataIndex: 'isActive',
    width: 100,
    customRender: ({ text }) => {
      const isActive = text === '1';
      return h(Tag, {
        color: isActive ? 'success' : 'error'
      }, () => isActive ? '激活' : '未激活');
    }
   },
   {
    title: '创建人',
    align:"center",
    dataIndex: 'createBy',
    width: 120
   },
   {
    title: '创建时间',
    align:"center",
    dataIndex: 'createTime',
    width: 150
   },
   {
    title: '说明',
    align:"left",
    dataIndex: 'remark',
    width: 250,
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return '-';
      return text.length > 40 ? text.substring(0, 40) + '...' : text;
    }
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '模板名称',
    field: 'templateName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '状态',
    field: 'isActive',
    component: 'Select',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '激活', value: '1' },
        { label: '未激活', value: '0' }
      ]
    },
    colProps: { span: 6 },
  }
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '模板名称',
    field: 'templateName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入模板名称!'},
          ];
     },
  },
  {
    label: '部门角色选择',
    field: 'deptRoleSelection',
    component: 'JDeptRoleSelector',
    componentProps: {
      placeholder: '请选择部门和角色'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择部门和角色!'},
          ];
     },
  },
  {
    label: '是否激活',
    field: 'isActive',
    component: 'RadioButtonGroup',
    defaultValue: '0', // 默认未激活
    componentProps: {
      options: [
        { label: '未激活', value: '0' },
        { label: '激活', value: '1' }
      ]
    },
    // 只在编辑时显示激活选项，新增时隐藏
    ifShow: ({values}) => {
      // 如果有id字段，说明是编辑模式
      return !!values.id;
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择是否激活!'},
          ];
     },
  },
  {
    label: '说明',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
      placeholder: '请输入模板说明...'
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  templateName: {title: '模板名称',order: 0,view: 'text', type: 'string',},
  isActive: {title: '是否激活（1=激活，0=未激活）',order: 1,view: 'radio', type: 'string',dictCode: '',},
  remark: {title: '说明',order: 2,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
